package fr._42.cinema.controller;

import fr._42.cinema.models.ChatMessage;
import fr._42.cinema.services.ChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
public class FilmChatController {
    private final ChatService chatService;

    @Autowired
    public FilmChatController(ChatService chatService) {
        this.chatService = chatService;
    }

    @MessageMapping("/films/{filmId}/chat/send")
    @SendTo("/topic/films/{filmId}/chat/messages")
    public ChatMessage sendMessage(
            @DestinationVariable("filmId") Long filmId,
            @Payload ChatMessage chatMessage
    ) {
        chatMessage.setFilmId(filmId);
        return chatService.saveMessage(chatMessage);
    }

    @GetMapping("/films/{id}/chat")
    public String filmChatPage(@PathVariable("id") Long filmId, Model model, HttpServletRequest request) {
        List<ChatMessage> messages = chatService.getLast20Messages(filmId);

        model.addAttribute("filmId", filmId);
        model.addAttribute("messages", messages);
        model.addAttribute("userIp", request.getRemoteAddr());

        return "filmChat";
    }
}