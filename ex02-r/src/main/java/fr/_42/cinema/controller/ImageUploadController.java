package fr._42.cinema.controller;

import fr._42.cinema.models.UploadedImage;
import fr._42.cinema.repository.UploadedImageRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Controller
public class ImageUploadController {
    private static final Logger logger = LoggerFactory.getLogger(ImageUploadController.class);
    private static final String UPLOAD_DIR = "uploaded-images/";

    @Autowired
    private UploadedImageRepository uploadedImageRepository;

    public ImageUploadController() {
        try {
            Files.createDirectories(Paths.get(UPLOAD_DIR));
            logger.info("Upload directory created/verified: {}", UPLOAD_DIR);
        } catch (IOException e) {
            logger.error("Failed to create upload directory", e);
        }
    }
    
    @PostMapping("/images")
    @ResponseBody
    public String uploadImage(
            @RequestParam("file") MultipartFile file,
            @CookieValue(value = "userId", required = false) String userId,
            HttpServletRequest request
    ) {
        if (file.isEmpty()) {
            return "Error: No file selected";
        }

        try {
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFilename = UUID.randomUUID().toString() + extension;

            // Save file to disk
            Path filePath = Paths.get(UPLOAD_DIR + uniqueFilename);
            Files.write(filePath, file.getBytes());

            // Save metadata to database
            UploadedImage uploadedImage = new UploadedImage(originalFilename, uniqueFilename, userId);
            uploadedImageRepository.save(uploadedImage);

            logger.info("File uploaded: {} -> {}", originalFilename, uniqueFilename);
            return "Success: File uploaded as " + originalFilename;

        } catch (IOException e) {
            logger.error("Failed to upload file", e);
            return "Error: Failed to upload file";
        }
    }
    
    @GetMapping("/images/list")
    @ResponseBody
    public List<Map<String, String>> listImages() {
        List<Map<String, String>> imageList = uploadedImageRepository.findAllByOrderByUploadTimeDesc()
                .stream()
                .map(img -> {
                    Map<String, String> imageInfo = new HashMap<>();
                    imageInfo.put("originalName", img.getOriginalFilename());
                    imageInfo.put("storedName", img.getStoredFilename());
                    imageInfo.put("uploadTime", img.getUploadTime().toString());
                    return imageInfo;
                })
                .toList();
        return imageList;
    }

    @GetMapping("/uploaded-images/{filename}")
    @ResponseBody
    public byte[] getImage(@PathVariable String filename) {
        try {
            Path imagePath = Paths.get(UPLOAD_DIR + filename);
            if (Files.exists(imagePath)) {
                return Files.readAllBytes(imagePath);
            }
        } catch (IOException e) {
            logger.error("Failed to read image: {}", filename, e);
        }
        return new byte[0];
    }
}
