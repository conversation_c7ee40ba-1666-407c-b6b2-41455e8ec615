package fr._42.cinema.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Controller
public class ImageUploadController {
    private static final Logger logger = LoggerFactory.getLogger(ImageUploadController.class);
    private static final String UPLOAD_DIR = "uploaded-images/";
    
    public ImageUploadController() {
        // Create upload directory if it doesn't exist
        try {
            Files.createDirectories(Paths.get(UPLOAD_DIR));
            logger.info("Upload directory created/verified: {}", UPLOAD_DIR);
        } catch (IOException e) {
            logger.error("Failed to create upload directory", e);
        }
    }
    
    @PostMapping("/images")
    @ResponseBody
    public String uploadImage(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return "Error: No file selected";
        }
        
        try {
            // Generate unique filename
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFilename = UUID.randomUUID().toString() + extension;
            
            // Save file
            Path filePath = Paths.get(UPLOAD_DIR + uniqueFilename);
            Files.write(filePath, file.getBytes());
            
            logger.info("File uploaded: {} -> {}", originalFilename, uniqueFilename);
            return "Success: File uploaded as " + uniqueFilename;
            
        } catch (IOException e) {
            logger.error("Failed to upload file", e);
            return "Error: Failed to upload file";
        }
    }
    
    @GetMapping("/images/list")
    @ResponseBody
    public List<String> listImages() {
        List<String> imageList = new ArrayList<>();
        try {
            File uploadDir = new File(UPLOAD_DIR);
            if (uploadDir.exists() && uploadDir.isDirectory()) {
                File[] files = uploadDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile()) {
                            imageList.add(file.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to list images", e);
        }
        return imageList;
    }
}
